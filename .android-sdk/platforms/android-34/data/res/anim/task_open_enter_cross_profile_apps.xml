<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2018, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<!-- This should in sync with task_open_enter.xml -->
<set xmlns:android="http://schemas.android.com/apk/res/android"
     android:shareInterpolator="false"
     android:zAdjustment="top"
     android:hasRoundedCorners="true">

    <translate
        android:fromXDelta="105%"
        android:toXDelta="0"
        android:fillEnabled="true"
        android:fillBefore="true"
        android:fillAfter="true"
        android:interpolator="@interpolator/fast_out_extra_slow_in"
        android:startOffset="0"
        android:duration="500"/>

    <!-- To keep the transition around longer for the thumbnail, should be kept in sync with
         cross_profile_apps_thumbmail.xml -->
    <alpha
        android:fromAlpha="1.0"
        android:toAlpha="1.0"
        android:startOffset="717"
        android:duration="200"/>
</set>