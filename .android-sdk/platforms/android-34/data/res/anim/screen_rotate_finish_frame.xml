<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<set xmlns:android="http://schemas.android.com/apk/res/android"
        android:shareInterpolator="false">
    <scale android:fromXScale="1.0" android:toXScale="1.1111111111111"
            android:fromYScale="1.0" android:toYScale="1.1111111111111"
            android:pivotX="50%" android:pivotY="50%"
            android:interpolator="@interpolator/accelerate_decelerate"
            android:fillEnabled="true"
            android:fillBefore="true" android:fillAfter="true"
            android:duration="@android:integer/config_shortAnimTime"/>
    <scale android:fromXScale="100%" android:toXScale="100%p"
            android:fromYScale="100%" android:toYScale="100%p"
            android:pivotX="50%" android:pivotY="50%"
            android:fillEnabled="true"
            android:fillBefore="true" android:fillAfter="true"
            android:interpolator="@interpolator/accelerate_decelerate"
            android:duration="@android:integer/config_shortAnimTime" />
</set>
