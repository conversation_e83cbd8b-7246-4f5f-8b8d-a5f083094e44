<?xml version="1.0" encoding="utf-8"?>
<!--
/* Copyright 2015, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<set
    xmlns:android="http://schemas.android.com/apk/res/android" >
    <objectAnimator
        android:duration="450"
        android:propertyXName="translateX"
        android:propertyYName="translateY"
        android:pathData="M 4.5,0.0 c -0.75,0.0 -3.75,0.0 -4.5,0.0"
        android:interpolator="@interpolator/ft_avd_toarrow_animation_interpolator_0" />
</set>
